🎉 高级自动点击器 v2.0 更新说明
=====================================

📅 更新日期：2025-08-10
🚀 版本：v2.0 (AutoClicker_v2.exe)

🆕 主要新功能
=============

1. 🔧 自定义全局快捷键
   - 不再固定使用F1-F3，避免与其他软件冲突
   - 支持任意组合键：Ctrl+键、Alt+键、Shift+键等
   - 默认快捷键：Ctrl+F9(开始/停止)、Ctrl+F10(紧急停止)、Ctrl+F11(暂停/继续)
   - 在"高级设置"标签页中可以自定义

2. 🎯 智能按键输入
   - 按键选择不再使用下拉菜单
   - 点击输入框后直接按键盘即可设置
   - 自动识别所有类型按键（字母、数字、功能键、特殊键等）
   - 支持清除和重新设置

3. 💾 配置自动保存
   - 快捷键设置会自动保存到配置文件
   - 程序重启后自动加载上次的设置
   - 支持配置重置功能

🔄 改进功能
===========

1. 🎨 界面优化
   - 按钮显示当前设置的快捷键
   - 更清晰的操作提示
   - 更好的用户体验

2. 🛡️ 稳定性提升
   - 更好的错误处理
   - 更稳定的全局快捷键监听
   - 支持复杂的组合键

3. 📝 详细日志
   - 记录快捷键设置变更
   - 更详细的操作反馈

🎮 使用方法
===========

快速设置自定义快捷键：
1. 运行 AutoClicker_v2.exe
2. 切换到"高级设置"标签页
3. 点击要设置的快捷键输入框
4. 按下您想要的组合键（如 Ctrl+Space）
5. 点击"应用快捷键设置"
6. 完成！现在可以使用新的快捷键了

推荐快捷键组合：
- Ctrl + F9/F10/F11/F12
- Alt + Space/Enter/Tab
- Ctrl + Alt + 字母键
- Shift + F1-F12

⚠️ 重要提醒
===========

1. 如果您之前使用的是旧版本，建议使用新版本 AutoClicker_v2.exe
2. 旧版本的F1-F3快捷键可能与其他软件冲突
3. 新版本默认使用 Ctrl+F9/F10/F11，冲突概率更低
4. 如果仍有冲突，可以自定义为任意组合键

🔧 技术改进
===========

- 使用更先进的快捷键监听机制
- 支持更复杂的组合键解析
- 更好的键盘事件处理
- 优化的配置管理系统

📁 文件说明
===========

- AutoClicker_v2.exe    ← 新版本主程序（推荐使用）
- AutoClicker.exe       ← 旧版本程序
- 使用说明.txt          ← 详细使用说明
- 更新说明_v2.0.txt     ← 本文件

🎊 感谢使用！
=============

新版本解决了快捷键冲突的问题，提供了更好的用户体验。
如有任何问题或建议，请查看使用说明.txt文件。

祝您使用愉快！
