🎮 AutoClicker v2.1 按钮功能说明
===================================

界面底部控制按钮布局（从左到右）：

┌─────────────┬─────────────┬─────────────┬─────────────┐
│ 🟢 开始按钮  │ 🟡 暂停按钮  │ 🔵 手动暂停  │ 🔴 停止按钮  │
│             │             │             │             │
│ 启动自动点击 │ 快捷键暂停   │ 手动暂停    │ 完全停止    │
│ Ctrl+F9     │ Ctrl+F11    │ 不依赖快捷键 │ Ctrl+F10    │
└─────────────┴─────────────┴─────────────┴─────────────┘

📋 按钮状态详解
===============

🔸 程序未运行时：
   ✅ 开始按钮：绿色，可点击
   ❌ 暂停按钮：灰色，禁用
   ❌ 手动暂停：灰色，禁用  
   ❌ 停止按钮：灰色，禁用

🔸 程序运行时：
   ❌ 开始按钮：灰色，禁用
   ✅ 暂停按钮：黄色，显示"暂停"
   ✅ 手动暂停：蓝色，显示"手动暂停"
   ✅ 停止按钮：红色，可点击

🔸 程序暂停时：
   ❌ 开始按钮：灰色，禁用
   ✅ 暂停按钮：黄色，显示"继续"
   ✅ 手动暂停：绿色，显示"手动继续"
   ✅ 停止按钮：红色，可点击

🎯 使用建议
===========

1. **正常使用**：开始 → 暂停/继续 → 停止
2. **快捷键失效时**：开始 → 手动暂停/继续 → 停止
3. **紧急情况**：直接点击停止按钮
4. **混合使用**：快捷键和手动按钮可以混合使用

🔧 快捷键说明
=============

- 所有快捷键都可以在"高级设置"中自定义
- 默认快捷键：Ctrl+F9/F10/F11
- 手动暂停按钮不依赖任何快捷键
- 快捷键和按钮功能完全同步

💡 特别提醒
===========

- "暂停按钮"和"手动暂停按钮"功能相同，只是触发方式不同
- 手动暂停按钮是v2.1新增功能，专门解决快捷键无法使用的问题
- 两个暂停按钮的状态会同步变化
- 建议优先使用快捷键，快捷键失效时使用手动按钮
