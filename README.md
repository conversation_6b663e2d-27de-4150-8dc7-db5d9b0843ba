# 高级自动点击器 v2.0

一个功能强大的Windows自动点击工具，支持键盘按键和鼠标点击。

## 功能特点

- ✅ **双模式支持**: 键盘按键 + 鼠标点击
- ✅ **全局快捷键**: 无需窗口焦点即可控制
- ✅ **智能暂停**: 支持暂停/继续功能
- ✅ **点击限制**: 可设置最大点击次数
- ✅ **实时监控**: 显示运行状态和统计信息
- ✅ **配置保存**: 自动保存和加载设置
- ✅ **操作日志**: 详细的操作记录
- ✅ **用户友好**: 现代化的图形界面

## 快速开始

### 方法1: 直接运行可执行文件
1. 双击运行 `AutoClicker.exe`
2. 根据需要配置设置
3. 点击"开始"或按F1开始自动点击

### 方法2: 从源码运行
1. 安装Python 3.7+
2. 安装依赖: `pip install -r requirements.txt`
3. 运行: `python keyboard_autoclicker.py`

## 打包说明

### 自动打包
运行 `build.bat` 进行完整打包，或运行 `quick_build.bat` 进行快速打包。

### 手动打包
```bash
pip install pyautogui pynput pyinstaller
pyinstaller --onefile --windowed --name="AutoClicker" keyboard_autoclicker.py
```

## 使用说明

### 全局快捷键
- **F1**: 开始/停止自动点击
- **F2**: 紧急停止
- **F3**: 暂停/继续

### 键盘模式
1. 选择"键盘按键"
2. 从下拉菜单选择目标按键
3. 设置点击间隔
4. 可选择设置点击次数限制

### 鼠标模式
1. 选择"鼠标点击"
2. 选择鼠标按钮（左键/右键/中键）
3. 设置点击坐标（可使用"获取当前位置"按钮）
4. 设置点击间隔

### 高级功能
- **配置管理**: 保存/加载/重置配置
- **状态监控**: 实时查看运行状态和统计
- **操作日志**: 查看详细的操作记录

## 系统要求

- Windows 7/8/10/11
- Python 3.7+ (仅源码运行需要)

## 依赖库

- `pyautogui`: 自动化操作
- `pynput`: 全局快捷键监听
- `tkinter`: 图形界面 (Python内置)

## 注意事项

1. **管理员权限**: 某些应用可能需要管理员权限才能正常工作
2. **防病毒软件**: 可能会误报，请添加到白名单
3. **安全使用**: 请合理使用，避免在重要操作中误触发
4. **快捷键冲突**: 如果F1-F3与其他软件冲突，请先关闭其他软件

## 更新日志

### v2.0
- 全新的现代化界面设计
- 添加鼠标点击支持
- 实现真正的全局快捷键
- 添加暂停/继续功能
- 增加点击次数限制
- 添加配置保存/加载
- 实时状态监控和日志记录
- 更好的错误处理和用户反馈

### v1.0
- 基础键盘自动点击功能
- 简单的图形界面
- 基本的快捷键支持

## 许可证

本项目仅供学习和个人使用，请勿用于商业用途或恶意行为。

## 技术支持

如有问题或建议，请通过以下方式联系：
- 提交Issue到项目仓库
- 发送邮件说明问题详情

---

**免责声明**: 使用本软件造成的任何后果由用户自行承担，开发者不承担任何责任。
