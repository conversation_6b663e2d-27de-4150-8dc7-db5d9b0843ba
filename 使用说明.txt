高级自动点击器 v2.0 使用说明
=====================================

🎉 恭喜！您已成功获得了全新的高级自动点击器！

📁 文件说明
-----------
- AutoClicker_v2.2.exe     ← 最新版本（推荐使用）🌟
- keyboard_autoclicker.py  ← 源代码文件
- README.md                ← 详细说明文档
- 使用说明.txt             ← 本文件

🚀 快速开始
-----------
1. 双击 AutoClicker_v2.2.exe 启动程序
2. 选择动作类型（键盘按键 或 鼠标点击）
3. 设置相关参数
4. 点击"开始"按钮开始自动点击
5. 界面底部有4个控制按钮可以控制程序

⌨️ 按键设置（新功能！）
---------------------
1. 点击"目标按键"输入框
2. 直接按下您想要的按键
3. 按键会自动显示在输入框中
4. 支持所有键盘按键，包括：
   - 字母键：a-z
   - 数字键：0-9
   - 功能键：F1-F12
   - 特殊键：空格、回车、Tab等
   - 方向键：上下左右
   - 修饰键：Ctrl、Alt、Shift等

🖱️ 鼠标设置
-----------
1. 选择鼠标按钮（左键/右键/中键）
2. 设置点击坐标
3. 使用"获取当前位置"按钮快速获取鼠标位置

🎮 全局快捷键（可自定义！）
-----------------------
默认快捷键：
- Ctrl+F9：开始/停止自动点击
- Ctrl+F10：紧急停止
- Ctrl+F11：暂停/继续

🔧 自定义快捷键
--------------
1. 打开程序，切换到"高级设置"标签页
2. 点击快捷键输入框
3. 按下您想要的组合键（如 Ctrl+Space, Alt+F1 等）
4. 点击"应用快捷键设置"
5. 支持的组合键：
   - Ctrl + 任意键
   - Alt + 任意键
   - Shift + 任意键
   - 多重组合：Ctrl+Alt+键 等

✨ 高级功能
-----------
- 点击次数限制：可设置最大点击次数
- 实时监控：查看运行状态和统计信息
- 配置保存：自动保存和加载设置
- 操作日志：详细记录所有操作
- 自定义快捷键：避免与其他软件冲突
- 智能按键捕获：点击输入框直接按键设置
- 手动暂停按钮：不依赖快捷键的暂停控制 🆕

🎮 界面按钮说明
--------------
程序界面底部有4个控制按钮（从左到右）：

1. **🟢 开始按钮**
   - 启动自动点击功能
   - 快捷键：Ctrl+F9（可自定义）

2. **🟡 暂停按钮**
   - 快捷键暂停/继续
   - 快捷键：Ctrl+F11（可自定义）

3. **🔵 手动暂停按钮**（新功能）
   - 手动暂停/继续，不依赖快捷键
   - 暂停时变为绿色"手动继续"

4. **🔴 停止按钮**
   - 完全停止自动点击
   - 快捷键：Ctrl+F10（可自定义）

⚠️ 注意事项
-----------
1. 首次运行可能被防病毒软件拦截，请添加到白名单
2. 某些程序可能需要管理员权限才能正常工作
3. 请合理使用，避免在重要操作中误触发
4. 如果默认快捷键冲突，请在"高级设置"中自定义快捷键
5. 建议使用不常用的组合键，如 Ctrl+F9-F12
6. 快捷键无法使用时，可以使用手动按钮进行控制

🔧 故障排除
-----------
- 如果程序无法启动，请确保系统支持.NET Framework
- 如果快捷键不工作，请尝试以管理员身份运行
- 如果鼠标点击不准确，请重新获取坐标位置

📞 技术支持
-----------
如有问题或建议，请查看README.md文件获取更多信息。

祝您使用愉快！ 🎊
