高级自动点击器 v2.0 使用说明
=====================================

🎉 恭喜！您已成功获得了全新的高级自动点击器！

📁 文件说明
-----------
- AutoClicker.exe          ← 主程序（双击运行）
- keyboard_autoclicker.py  ← 源代码文件
- README.md                ← 详细说明文档
- 使用说明.txt             ← 本文件

🚀 快速开始
-----------
1. 双击 AutoClicker.exe 启动程序
2. 选择动作类型（键盘按键 或 鼠标点击）
3. 设置相关参数
4. 点击"开始"或按F1键开始自动点击

⌨️ 按键设置（新功能！）
---------------------
1. 点击"目标按键"输入框
2. 直接按下您想要的按键
3. 按键会自动显示在输入框中
4. 支持所有键盘按键，包括：
   - 字母键：a-z
   - 数字键：0-9
   - 功能键：F1-F12
   - 特殊键：空格、回车、Tab等
   - 方向键：上下左右
   - 修饰键：Ctrl、Alt、Shift等

🖱️ 鼠标设置
-----------
1. 选择鼠标按钮（左键/右键/中键）
2. 设置点击坐标
3. 使用"获取当前位置"按钮快速获取鼠标位置

🎮 全局快捷键
-----------
- F1：开始/停止自动点击
- F2：紧急停止
- F3：暂停/继续

✨ 高级功能
-----------
- 点击次数限制：可设置最大点击次数
- 实时监控：查看运行状态和统计信息
- 配置保存：自动保存和加载设置
- 操作日志：详细记录所有操作

⚠️ 注意事项
-----------
1. 首次运行可能被防病毒软件拦截，请添加到白名单
2. 某些程序可能需要管理员权限才能正常工作
3. 请合理使用，避免在重要操作中误触发
4. 如果快捷键与其他软件冲突，请先关闭其他软件

🔧 故障排除
-----------
- 如果程序无法启动，请确保系统支持.NET Framework
- 如果快捷键不工作，请尝试以管理员身份运行
- 如果鼠标点击不准确，请重新获取坐标位置

📞 技术支持
-----------
如有问题或建议，请查看README.md文件获取更多信息。

祝您使用愉快！ 🎊
