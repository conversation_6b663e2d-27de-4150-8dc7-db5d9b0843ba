import tkinter as tk
from tkinter import messagebox, ttk
import threading
import time
import json
import os
from datetime import datetime
import sys

try:
    import pyautogui
    import pynput
    from pynput import keyboard, mouse
    from pynput.keyboard import Key, Listener as KeyboardListener
    from pynput.mouse import <PERSON><PERSON>, Listener as MouseListener
except ImportError as e:
    print(f"缺少必要的库: {e}")
    print("请运行: pip install pyautogui pynput")
    sys.exit(1)

class AutoClicker:
    def __init__(self, root):
        self.root = root
        self.root.title('高级自动点击器 v2.0')
        self.root.geometry('450x600')
        self.root.resizable(False, False)

        # 设置窗口图标和样式
        self.setup_window_style()

        # 状态变量
        self.running = False
        self.paused = False
        self.click_count = 0
        self.total_clicks = 0
        self.start_time = None

        # 配置变量
        self.interval = 1.0
        self.action_type = "keyboard"  # keyboard 或 mouse
        self.key = 'a'
        self.mouse_button = 'left'
        self.click_limit = 0  # 0表示无限制
        self.mouse_x = 0
        self.mouse_y = 0

        # 全局快捷键监听器
        self.keyboard_listener = None
        self.start_hotkey_combo = "ctrl+f9"  # 默认快捷键
        self.stop_hotkey_combo = "ctrl+f10"
        self.pause_hotkey_combo = "ctrl+f11"
        self.current_keys = set()
        self.hotkey_pressed = set()

        # 配置文件路径
        self.config_file = "autoclicker_config.json"

        # 创建界面
        self.create_widgets()

        # 加载配置
        self.load_config()

        # 启动全局快捷键监听
        self.start_global_hotkeys()

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_window_style(self):
        """设置窗口样式"""
        try:
            # 设置窗口在任务栏中的图标
            self.root.iconbitmap(default='')
        except:
            pass

        # 设置主题色
        self.bg_color = '#f0f0f0'
        self.accent_color = '#0078d4'
        self.root.configure(bg=self.bg_color)

    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg=self.bg_color)
        title_frame.pack(fill='x', padx=10, pady=10)

        title_label = tk.Label(title_frame, text='高级自动点击器',
                              font=('微软雅黑', 16, 'bold'),
                              bg=self.bg_color, fg=self.accent_color)
        title_label.pack()

        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)

        # 基本设置标签页
        self.basic_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.basic_frame, text='基本设置')
        self.create_basic_settings()

        # 高级设置标签页
        self.advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.advanced_frame, text='高级设置')
        self.create_advanced_settings()

        # 状态监控标签页
        self.status_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.status_frame, text='状态监控')
        self.create_status_monitor()

        # 控制按钮
        self.create_control_buttons()

    def create_basic_settings(self):
        """创建基本设置界面"""
        # 动作类型选择
        action_frame = ttk.LabelFrame(self.basic_frame, text='动作类型', padding=10)
        action_frame.pack(fill='x', padx=10, pady=5)

        self.action_var = tk.StringVar(value="keyboard")
        ttk.Radiobutton(action_frame, text='键盘按键', variable=self.action_var,
                       value='keyboard', command=self.on_action_type_change).pack(anchor='w')
        ttk.Radiobutton(action_frame, text='鼠标点击', variable=self.action_var,
                       value='mouse', command=self.on_action_type_change).pack(anchor='w')

        # 键盘设置框架
        self.keyboard_frame = ttk.LabelFrame(self.basic_frame, text='键盘设置', padding=10)
        self.keyboard_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(self.keyboard_frame, text='目标按键:').pack(anchor='w')

        # 创建按键输入框架
        key_input_frame = ttk.Frame(self.keyboard_frame)
        key_input_frame.pack(fill='x', pady=2)

        self.key_entry = ttk.Entry(key_input_frame, width=15)
        self.key_entry.pack(side='left', fill='x', expand=True)
        self.key_entry.insert(0, 'a')

        # 绑定按键捕获事件
        self.key_entry.bind('<FocusIn>', self.on_key_entry_focus)
        self.key_entry.bind('<KeyPress>', self.on_key_press)
        self.key_entry.bind('<FocusOut>', self.on_key_entry_unfocus)

        # 添加说明标签
        ttk.Label(key_input_frame, text='← 点击后按键',
                 foreground='gray').pack(side='left', padx=(5,0))

        # 添加清除按钮
        ttk.Button(key_input_frame, text='清除', width=6,
                  command=self.clear_key_entry).pack(side='right', padx=(5,0))

        # 按键捕获状态
        self.capturing_key = False

        # 鼠标设置框架
        self.mouse_frame = ttk.LabelFrame(self.basic_frame, text='鼠标设置', padding=10)
        self.mouse_frame.pack(fill='x', padx=10, pady=5)

        # 鼠标按钮选择
        button_frame = ttk.Frame(self.mouse_frame)
        button_frame.pack(fill='x', pady=2)

        ttk.Label(button_frame, text='鼠标按钮:').pack(side='left')
        self.mouse_button_var = tk.StringVar(value="left")
        ttk.Radiobutton(button_frame, text='左键', variable=self.mouse_button_var,
                       value='left').pack(side='left', padx=10)
        ttk.Radiobutton(button_frame, text='右键', variable=self.mouse_button_var,
                       value='right').pack(side='left', padx=10)
        ttk.Radiobutton(button_frame, text='中键', variable=self.mouse_button_var,
                       value='middle').pack(side='left', padx=10)

        # 鼠标位置设置
        pos_frame = ttk.Frame(self.mouse_frame)
        pos_frame.pack(fill='x', pady=5)

        ttk.Label(pos_frame, text='点击位置:').pack(anchor='w')

        coord_frame = ttk.Frame(pos_frame)
        coord_frame.pack(fill='x', pady=2)

        ttk.Label(coord_frame, text='X:').pack(side='left')
        self.mouse_x_entry = ttk.Entry(coord_frame, width=8)
        self.mouse_x_entry.pack(side='left', padx=5)

        ttk.Label(coord_frame, text='Y:').pack(side='left', padx=(10,0))
        self.mouse_y_entry = ttk.Entry(coord_frame, width=8)
        self.mouse_y_entry.pack(side='left', padx=5)

        ttk.Button(coord_frame, text='获取当前位置',
                  command=self.get_current_mouse_pos).pack(side='left', padx=10)

        # 时间间隔设置
        interval_frame = ttk.LabelFrame(self.basic_frame, text='时间设置', padding=10)
        interval_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(interval_frame, text='点击间隔（秒）:').pack(anchor='w')
        self.interval_entry = ttk.Entry(interval_frame, width=20)
        self.interval_entry.pack(fill='x', pady=2)
        self.interval_entry.insert(0, '1.0')

        # 点击次数限制
        limit_frame = ttk.Frame(interval_frame)
        limit_frame.pack(fill='x', pady=5)

        self.limit_var = tk.BooleanVar()
        ttk.Checkbutton(limit_frame, text='限制点击次数',
                       variable=self.limit_var,
                       command=self.on_limit_change).pack(side='left')

        self.limit_entry = ttk.Entry(limit_frame, width=10, state='disabled')
        self.limit_entry.pack(side='left', padx=10)

        # 初始化界面状态
        self.on_action_type_change()

    def create_advanced_settings(self):
        """创建高级设置界面"""
        # 全局快捷键设置
        hotkey_frame = ttk.LabelFrame(self.advanced_frame, text='全局快捷键设置', padding=10)
        hotkey_frame.pack(fill='x', padx=10, pady=5)

        # 开始/停止快捷键
        start_frame = ttk.Frame(hotkey_frame)
        start_frame.pack(fill='x', pady=2)
        ttk.Label(start_frame, text='开始/停止:', width=12).pack(side='left')
        self.start_hotkey_entry = ttk.Entry(start_frame, width=15)
        self.start_hotkey_entry.pack(side='left', padx=5)
        self.start_hotkey_entry.insert(0, self.start_hotkey_combo)
        self.start_hotkey_entry.bind('<FocusIn>', lambda e: self.start_hotkey_capture(e, 'start'))
        self.start_hotkey_entry.bind('<KeyPress>', lambda e: self.capture_hotkey_combo(e, 'start'))
        ttk.Button(start_frame, text='清除', width=6,
                  command=lambda: self.clear_hotkey_entry('start')).pack(side='left', padx=2)

        # 紧急停止快捷键
        stop_frame = ttk.Frame(hotkey_frame)
        stop_frame.pack(fill='x', pady=2)
        ttk.Label(stop_frame, text='紧急停止:', width=12).pack(side='left')
        self.stop_hotkey_entry = ttk.Entry(stop_frame, width=15)
        self.stop_hotkey_entry.pack(side='left', padx=5)
        self.stop_hotkey_entry.insert(0, self.stop_hotkey_combo)
        self.stop_hotkey_entry.bind('<FocusIn>', lambda e: self.start_hotkey_capture(e, 'stop'))
        self.stop_hotkey_entry.bind('<KeyPress>', lambda e: self.capture_hotkey_combo(e, 'stop'))
        ttk.Button(stop_frame, text='清除', width=6,
                  command=lambda: self.clear_hotkey_entry('stop')).pack(side='left', padx=2)

        # 暂停/继续快捷键
        pause_frame = ttk.Frame(hotkey_frame)
        pause_frame.pack(fill='x', pady=2)
        ttk.Label(pause_frame, text='暂停/继续:', width=12).pack(side='left')
        self.pause_hotkey_entry = ttk.Entry(pause_frame, width=15)
        self.pause_hotkey_entry.pack(side='left', padx=5)
        self.pause_hotkey_entry.insert(0, self.pause_hotkey_combo)
        self.pause_hotkey_entry.bind('<FocusIn>', lambda e: self.start_hotkey_capture(e, 'pause'))
        self.pause_hotkey_entry.bind('<KeyPress>', lambda e: self.capture_hotkey_combo(e, 'pause'))
        ttk.Button(pause_frame, text='清除', width=6,
                  command=lambda: self.clear_hotkey_entry('pause')).pack(side='left', padx=2)

        # 应用快捷键按钮
        apply_frame = ttk.Frame(hotkey_frame)
        apply_frame.pack(fill='x', pady=10)
        ttk.Button(apply_frame, text='应用快捷键设置',
                  command=self.apply_hotkey_settings).pack(side='left')
        ttk.Label(apply_frame, text='← 点击输入框后按组合键',
                 foreground='gray').pack(side='left', padx=10)

        # 快捷键说明
        info_frame = ttk.Frame(hotkey_frame)
        info_frame.pack(fill='x', pady=5)
        info_text = "支持组合键：Ctrl+键、Alt+键、Shift+键、Ctrl+Alt+键等\n示例：ctrl+f9, alt+space, shift+f1"
        ttk.Label(info_frame, text=info_text, foreground='blue',
                 font=('微软雅黑', 8)).pack(anchor='w')

        # 快捷键捕获状态
        self.capturing_hotkey = False
        self.current_hotkey_type = None

        # 配置管理
        config_frame = ttk.LabelFrame(self.advanced_frame, text='配置管理', padding=10)
        config_frame.pack(fill='x', padx=10, pady=5)

        config_btn_frame = ttk.Frame(config_frame)
        config_btn_frame.pack(fill='x')

        ttk.Button(config_btn_frame, text='保存配置',
                  command=self.save_config).pack(side='left', padx=5)
        ttk.Button(config_btn_frame, text='加载配置',
                  command=self.load_config).pack(side='left', padx=5)
        ttk.Button(config_btn_frame, text='重置配置',
                  command=self.reset_config).pack(side='left', padx=5)

    def create_status_monitor(self):
        """创建状态监控界面"""
        # 运行状态
        status_frame = ttk.LabelFrame(self.status_frame, text='运行状态', padding=10)
        status_frame.pack(fill='x', padx=10, pady=5)

        self.status_label = ttk.Label(status_frame, text='状态: 已停止',
                                     font=('微软雅黑', 10, 'bold'))
        self.status_label.pack(anchor='w', pady=2)

        # 统计信息
        stats_frame = ttk.LabelFrame(self.status_frame, text='统计信息', padding=10)
        stats_frame.pack(fill='x', padx=10, pady=5)

        self.click_count_label = ttk.Label(stats_frame, text='当前会话点击次数: 0')
        self.click_count_label.pack(anchor='w', pady=2)

        self.total_clicks_label = ttk.Label(stats_frame, text='总点击次数: 0')
        self.total_clicks_label.pack(anchor='w', pady=2)

        self.runtime_label = ttk.Label(stats_frame, text='运行时间: 00:00:00')
        self.runtime_label.pack(anchor='w', pady=2)

        # 日志区域
        log_frame = ttk.LabelFrame(self.status_frame, text='操作日志', padding=10)
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # 创建文本框和滚动条
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.pack(fill='both', expand=True)

        self.log_text = tk.Text(log_text_frame, height=8, wrap='word', state='disabled')
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side='left', fill='both', expand=True)
        log_scrollbar.pack(side='right', fill='y')

        # 清除日志按钮
        ttk.Button(log_frame, text='清除日志', command=self.clear_log).pack(pady=5)

    def create_control_buttons(self):
        """创建控制按钮"""
        control_frame = tk.Frame(self.root, bg=self.bg_color)
        control_frame.pack(fill='x', padx=10, pady=10)

        # 主要控制按钮
        btn_frame = tk.Frame(control_frame, bg=self.bg_color)
        btn_frame.pack()

        self.start_btn = tk.Button(btn_frame, text=f'开始 ({self.start_hotkey_combo})',
                                  command=self.start_clicking,
                                  bg='#28a745', fg='white',
                                  font=('微软雅黑', 10, 'bold'),
                                  width=15, height=2)
        self.start_btn.pack(side='left', padx=5)

        self.pause_btn = tk.Button(btn_frame, text=f'暂停 ({self.pause_hotkey_combo})',
                                  command=self.pause_clicking,
                                  bg='#ffc107', fg='black',
                                  font=('微软雅黑', 10, 'bold'),
                                  width=15, height=2, state='disabled')
        self.pause_btn.pack(side='left', padx=5)

        self.stop_btn = tk.Button(btn_frame, text=f'停止 ({self.stop_hotkey_combo})',
                                 command=self.stop_clicking,
                                 bg='#dc3545', fg='white',
                                 font=('微软雅黑', 10, 'bold'),
                                 width=15, height=2, state='disabled')
        self.stop_btn.pack(side='left', padx=5)

    def on_action_type_change(self):
        """动作类型改变时的处理"""
        if self.action_var.get() == 'keyboard':
            self.keyboard_frame.pack(fill='x', padx=10, pady=5)
            self.mouse_frame.pack_forget()
        else:
            self.mouse_frame.pack(fill='x', padx=10, pady=5)
            self.keyboard_frame.pack_forget()

    def on_limit_change(self):
        """点击次数限制改变时的处理"""
        if self.limit_var.get():
            self.limit_entry.config(state='normal')
            self.limit_entry.delete(0, tk.END)
            self.limit_entry.insert(0, '100')
        else:
            self.limit_entry.config(state='disabled')

    def get_current_mouse_pos(self):
        """获取当前鼠标位置"""
        try:
            x, y = pyautogui.position()
            self.mouse_x_entry.delete(0, tk.END)
            self.mouse_x_entry.insert(0, str(x))
            self.mouse_y_entry.delete(0, tk.END)
            self.mouse_y_entry.insert(0, str(y))
            self.log_message(f"获取鼠标位置: ({x}, {y})")
        except Exception as e:
            self.log_message(f"获取鼠标位置失败: {e}")

    def on_key_entry_focus(self, event):
        """按键输入框获得焦点时"""
        self.capturing_key = True
        self.key_entry.config(foreground='blue')
        self.key_entry.select_range(0, tk.END)
        self.log_message("请按下要设置的按键...")

    def on_key_entry_unfocus(self, event):
        """按键输入框失去焦点时"""
        self.capturing_key = False
        self.key_entry.config(foreground='black')

    def clear_key_entry(self):
        """清除按键输入框"""
        self.key_entry.delete(0, tk.END)
        self.key_entry.focus()

    def on_key_press(self, event):
        """处理按键输入"""
        if not self.capturing_key:
            return

        # 阻止默认行为
        event.widget.delete(0, tk.END)

        # 获取按键名称
        key_name = self.get_key_name(event)

        if key_name:
            event.widget.insert(0, key_name)
            self.log_message(f"设置按键: {key_name}")
            # 移除焦点，停止捕获
            self.root.focus()

        return "break"  # 阻止默认处理

    def get_key_name(self, event):
        """获取按键的标准名称"""
        # 特殊键映射
        special_keys = {
            'Return': 'enter',
            'BackSpace': 'backspace',
            'Tab': 'tab',
            'Escape': 'esc',
            'space': 'space',
            'Up': 'up',
            'Down': 'down',
            'Left': 'left',
            'Right': 'right',
            'Home': 'home',
            'End': 'end',
            'Page_Up': 'pageup',
            'Page_Down': 'pagedown',
            'Insert': 'insert',
            'Delete': 'delete',
            'Control_L': 'ctrl',
            'Control_R': 'ctrl',
            'Alt_L': 'alt',
            'Alt_R': 'alt',
            'Shift_L': 'shift',
            'Shift_R': 'shift',
            'Win_L': 'win',
            'Win_R': 'win',
            'Menu': 'menu',
            'Print': 'printscreen',
            'Scroll_Lock': 'scrolllock',
            'Pause': 'pause',
            'Caps_Lock': 'capslock',
            'Num_Lock': 'numlock'
        }

        # F键
        for i in range(1, 13):
            special_keys[f'F{i}'] = f'f{i}'

        # 数字键盘
        for i in range(10):
            special_keys[f'KP_{i}'] = f'num{i}'

        special_keys.update({
            'KP_Add': 'num+',
            'KP_Subtract': 'num-',
            'KP_Multiply': 'num*',
            'KP_Divide': 'num/',
            'KP_Decimal': 'num.',
            'KP_Enter': 'numenter'
        })

        # 获取按键符号
        keysym = event.keysym

        # 检查是否是特殊键
        if keysym in special_keys:
            return special_keys[keysym]

        # 普通字符键
        if len(keysym) == 1:
            return keysym.lower()

        # 其他情况返回原始符号
        return keysym.lower()

    def start_hotkey_capture(self, event, hotkey_type):
        """开始捕获快捷键组合"""
        self.capturing_hotkey = True
        self.current_hotkey_type = hotkey_type
        self.hotkey_pressed = set()
        event.widget.config(foreground='blue')
        event.widget.select_range(0, tk.END)
        self.log_message(f"请按下{hotkey_type}快捷键组合...")

    def clear_hotkey_entry(self, hotkey_type):
        """清除快捷键输入框"""
        if hotkey_type == 'start':
            self.start_hotkey_entry.delete(0, tk.END)
            self.start_hotkey_entry.focus()
        elif hotkey_type == 'stop':
            self.stop_hotkey_entry.delete(0, tk.END)
            self.stop_hotkey_entry.focus()
        elif hotkey_type == 'pause':
            self.pause_hotkey_entry.delete(0, tk.END)
            self.pause_hotkey_entry.focus()

    def capture_hotkey_combo(self, event, hotkey_type):
        """捕获快捷键组合"""
        if not self.capturing_hotkey or self.current_hotkey_type != hotkey_type:
            return

        # 阻止默认行为
        event.widget.delete(0, tk.END)

        # 构建组合键字符串
        combo_parts = []

        # 检查修饰键
        if event.state & 0x4:  # Ctrl
            combo_parts.append('ctrl')
        if event.state & 0x8:  # Alt
            combo_parts.append('alt')
        if event.state & 0x1:  # Shift
            combo_parts.append('shift')

        # 获取主键
        main_key = self.get_key_name(event)
        if main_key and main_key not in ['ctrl', 'alt', 'shift']:
            combo_parts.append(main_key)

        # 生成组合键字符串
        if combo_parts:
            combo_str = '+'.join(combo_parts)
            event.widget.insert(0, combo_str)
            self.log_message(f"设置{hotkey_type}快捷键: {combo_str}")

        # 停止捕获
        self.capturing_hotkey = False
        self.current_hotkey_type = None
        event.widget.config(foreground='black')
        self.root.focus()

        return "break"

    def apply_hotkey_settings(self):
        """应用快捷键设置"""
        try:
            # 获取新的快捷键设置
            new_start = self.start_hotkey_entry.get().strip()
            new_stop = self.stop_hotkey_entry.get().strip()
            new_pause = self.pause_hotkey_entry.get().strip()

            # 验证快捷键格式
            if not all([new_start, new_stop, new_pause]):
                messagebox.showerror('错误', '请设置所有快捷键')
                return

            # 检查是否有重复
            if len(set([new_start, new_stop, new_pause])) != 3:
                messagebox.showerror('错误', '快捷键不能重复')
                return

            # 更新快捷键设置
            self.start_hotkey_combo = new_start
            self.stop_hotkey_combo = new_stop
            self.pause_hotkey_combo = new_pause

            # 重新启动全局快捷键监听
            if self.keyboard_listener:
                self.keyboard_listener.stop()

            self.start_global_hotkeys()

            # 更新按钮文本
            self.start_btn.config(text=f'开始 ({new_start})')
            self.stop_btn.config(text=f'停止 ({new_stop})')
            self.pause_btn.config(text=f'暂停 ({new_pause})')

            self.log_message("快捷键设置已更新")
            messagebox.showinfo('成功', '快捷键设置已应用')

        except Exception as e:
            self.log_message(f"应用快捷键设置失败: {e}")
            messagebox.showerror('错误', f'应用快捷键设置失败: {e}')

    def validate_settings(self):
        """验证设置参数"""
        try:
            # 验证间隔时间
            interval = float(self.interval_entry.get())
            if interval <= 0:
                raise ValueError("间隔时间必须大于0")
            self.interval = interval

            # 验证点击次数限制
            if self.limit_var.get():
                limit = int(self.limit_entry.get())
                if limit <= 0:
                    raise ValueError("点击次数限制必须大于0")
                self.click_limit = limit
            else:
                self.click_limit = 0

            # 验证动作类型相关设置
            if self.action_var.get() == 'keyboard':
                self.action_type = 'keyboard'
                self.key = self.key_entry.get().strip()
                if not self.key:
                    raise ValueError("请输入目标按键")
            else:
                self.action_type = 'mouse'
                self.mouse_button = self.mouse_button_var.get()
                try:
                    self.mouse_x = int(self.mouse_x_entry.get())
                    self.mouse_y = int(self.mouse_y_entry.get())
                except ValueError:
                    raise ValueError("请输入有效的鼠标坐标")

            return True

        except ValueError as e:
            messagebox.showerror('设置错误', str(e))
            return False
        except Exception as e:
            messagebox.showerror('未知错误', f"验证设置时发生错误: {e}")
            return False

    def start_clicking(self):
        """开始点击"""
        if self.running:
            return

        if not self.validate_settings():
            return

        self.running = True
        self.paused = False
        self.click_count = 0
        self.start_time = datetime.now()

        # 更新界面状态
        self.start_btn.config(state='disabled')
        self.pause_btn.config(state='normal')
        self.stop_btn.config(state='normal')
        self.status_label.config(text='状态: 运行中')

        # 启动点击线程
        self.click_thread = threading.Thread(target=self.click_loop, daemon=True)
        self.click_thread.start()

        # 启动状态更新线程
        self.status_thread = threading.Thread(target=self.update_status, daemon=True)
        self.status_thread.start()

        action_desc = f"{'键盘按键: ' + self.key if self.action_type == 'keyboard' else '鼠标点击: ' + self.mouse_button + '键'}"
        self.log_message(f"开始自动点击 - {action_desc}, 间隔: {self.interval}秒")

    def pause_clicking(self):
        """暂停/继续点击"""
        if not self.running:
            return

        self.paused = not self.paused
        if self.paused:
            self.pause_btn.config(text='继续 (F3)')
            self.status_label.config(text='状态: 已暂停')
            self.log_message("暂停自动点击")
        else:
            self.pause_btn.config(text='暂停 (F3)')
            self.status_label.config(text='状态: 运行中')
            self.log_message("继续自动点击")

    def stop_clicking(self):
        """停止点击"""
        if not self.running:
            return

        self.running = False
        self.paused = False

        # 更新界面状态
        self.start_btn.config(state='normal')
        self.pause_btn.config(state='disabled', text='暂停 (F3)')
        self.stop_btn.config(state='disabled')
        self.status_label.config(text='状态: 已停止')

        # 更新总点击次数
        self.total_clicks += self.click_count

        self.log_message(f"停止自动点击 - 本次点击: {self.click_count} 次")

    def click_loop(self):
        """点击循环"""
        while self.running:
            if not self.paused:
                try:
                    # 检查点击次数限制
                    if self.click_limit > 0 and self.click_count >= self.click_limit:
                        self.root.after(0, self.stop_clicking)
                        break

                    # 执行点击动作
                    if self.action_type == 'keyboard':
                        pyautogui.press(self.key)
                    else:
                        if self.mouse_button == 'left':
                            pyautogui.click(self.mouse_x, self.mouse_y, button='left')
                        elif self.mouse_button == 'right':
                            pyautogui.click(self.mouse_x, self.mouse_y, button='right')
                        elif self.mouse_button == 'middle':
                            pyautogui.click(self.mouse_x, self.mouse_y, button='middle')

                    self.click_count += 1

                except Exception as e:
                    self.log_message(f"执行点击时发生错误: {e}")
                    self.root.after(0, self.stop_clicking)
                    break

            time.sleep(self.interval)

    def update_status(self):
        """更新状态显示"""
        while self.running:
            if self.start_time:
                runtime = datetime.now() - self.start_time
                runtime_str = str(runtime).split('.')[0]  # 去掉微秒

                self.root.after(0, lambda: self.click_count_label.config(
                    text=f'当前会话点击次数: {self.click_count}'))
                self.root.after(0, lambda: self.total_clicks_label.config(
                    text=f'总点击次数: {self.total_clicks + self.click_count}'))
                self.root.after(0, lambda: self.runtime_label.config(
                    text=f'运行时间: {runtime_str}'))

            time.sleep(1)

    def parse_hotkey_combo(self, combo_str):
        """解析快捷键组合字符串"""
        if not combo_str:
            return set()

        parts = combo_str.lower().split('+')
        keys = set()

        for part in parts:
            part = part.strip()
            if part == 'ctrl':
                keys.add(Key.ctrl_l)
                keys.add(Key.ctrl_r)  # 支持左右Ctrl
            elif part == 'alt':
                keys.add(Key.alt_l)
                keys.add(Key.alt_r)
            elif part == 'shift':
                keys.add(Key.shift_l)
                keys.add(Key.shift_r)
            elif part.startswith('f') and part[1:].isdigit():
                # 功能键
                f_num = int(part[1:])
                if 1 <= f_num <= 12:
                    keys.add(getattr(Key, f'f{f_num}'))
            elif len(part) == 1 and part.isalpha():
                # 字母键
                keys.add(keyboard.KeyCode.from_char(part))
            elif part == 'space':
                keys.add(Key.space)
            elif part == 'enter':
                keys.add(Key.enter)
            elif part == 'tab':
                keys.add(Key.tab)
            elif part == 'esc':
                keys.add(Key.esc)
            elif part in ['up', 'down', 'left', 'right']:
                keys.add(getattr(Key, part))
            elif len(part) == 1 and part.isdigit():
                # 数字键
                keys.add(keyboard.KeyCode.from_char(part))

        return keys

    def is_hotkey_pressed(self, combo_str):
        """检查指定的快捷键组合是否被按下"""
        target_keys = self.parse_hotkey_combo(combo_str)
        if not target_keys:
            return False

        # 检查是否所有目标键都被按下
        for target_key in target_keys:
            found = False
            if hasattr(target_key, 'vk'):
                # 特殊键（如Ctrl, Alt等）
                for pressed_key in self.current_keys:
                    if hasattr(pressed_key, 'vk') and pressed_key.vk == target_key.vk:
                        found = True
                        break
            else:
                # 普通键
                if target_key in self.current_keys:
                    found = True

            if not found:
                return False

        return True

    def start_global_hotkeys(self):
        """启动全局快捷键监听"""
        try:
            def on_press(key):
                self.current_keys.add(key)

                # 检查快捷键组合
                if self.is_hotkey_pressed(self.start_hotkey_combo):
                    if not self.running:
                        self.root.after(0, self.start_clicking)
                    else:
                        self.root.after(0, self.stop_clicking)
                elif self.is_hotkey_pressed(self.stop_hotkey_combo):
                    if self.running:
                        self.root.after(0, self.stop_clicking)
                elif self.is_hotkey_pressed(self.pause_hotkey_combo):
                    if self.running:
                        self.root.after(0, self.pause_clicking)

            def on_release(key):
                try:
                    self.current_keys.discard(key)
                except:
                    pass

            self.keyboard_listener = KeyboardListener(
                on_press=on_press,
                on_release=on_release
            )
            self.keyboard_listener.start()

        except Exception as e:
            self.log_message(f"启动全局快捷键失败: {e}")

    def log_message(self, message):
        """添加日志消息"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            self.log_text.config(state='normal')
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
            self.log_text.config(state='disabled')
        except Exception as e:
            print(f"日志记录失败: {e}")

    def clear_log(self):
        """清除日志"""
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')

    def save_config(self):
        """保存配置到文件"""
        try:
            config = {
                'interval': self.interval_entry.get(),
                'action_type': self.action_var.get(),
                'key': self.key_entry.get(),
                'mouse_button': self.mouse_button_var.get(),
                'mouse_x': self.mouse_x_entry.get(),
                'mouse_y': self.mouse_y_entry.get(),
                'click_limit_enabled': self.limit_var.get(),
                'click_limit': self.limit_entry.get(),
                'total_clicks': self.total_clicks,
                'start_hotkey': self.start_hotkey_combo,
                'stop_hotkey': self.stop_hotkey_combo,
                'pause_hotkey': self.pause_hotkey_combo
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            self.log_message("配置已保存")
            messagebox.showinfo('成功', '配置已保存到文件')

        except Exception as e:
            self.log_message(f"保存配置失败: {e}")
            messagebox.showerror('错误', f'保存配置失败: {e}')

    def load_config(self):
        """从文件加载配置"""
        try:
            if not os.path.exists(self.config_file):
                return

            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 应用配置
            self.interval_entry.delete(0, tk.END)
            self.interval_entry.insert(0, config.get('interval', '1.0'))

            self.action_var.set(config.get('action_type', 'keyboard'))
            self.on_action_type_change()

            self.key_entry.delete(0, tk.END)
            self.key_entry.insert(0, config.get('key', 'a'))
            self.mouse_button_var.set(config.get('mouse_button', 'left'))

            self.mouse_x_entry.delete(0, tk.END)
            self.mouse_x_entry.insert(0, config.get('mouse_x', '0'))

            self.mouse_y_entry.delete(0, tk.END)
            self.mouse_y_entry.insert(0, config.get('mouse_y', '0'))

            self.limit_var.set(config.get('click_limit_enabled', False))
            self.on_limit_change()
            if self.limit_var.get():
                self.limit_entry.delete(0, tk.END)
                self.limit_entry.insert(0, config.get('click_limit', '100'))

            self.total_clicks = config.get('total_clicks', 0)
            self.total_clicks_label.config(text=f'总点击次数: {self.total_clicks}')

            # 加载快捷键设置
            self.start_hotkey_combo = config.get('start_hotkey', 'ctrl+f9')
            self.stop_hotkey_combo = config.get('stop_hotkey', 'ctrl+f10')
            self.pause_hotkey_combo = config.get('pause_hotkey', 'ctrl+f11')

            # 更新快捷键输入框
            self.start_hotkey_entry.delete(0, tk.END)
            self.start_hotkey_entry.insert(0, self.start_hotkey_combo)

            self.stop_hotkey_entry.delete(0, tk.END)
            self.stop_hotkey_entry.insert(0, self.stop_hotkey_combo)

            self.pause_hotkey_entry.delete(0, tk.END)
            self.pause_hotkey_entry.insert(0, self.pause_hotkey_combo)

            # 更新按钮文本
            self.start_btn.config(text=f'开始 ({self.start_hotkey_combo})')
            self.stop_btn.config(text=f'停止 ({self.stop_hotkey_combo})')
            self.pause_btn.config(text=f'暂停 ({self.pause_hotkey_combo})')

            self.log_message("配置已加载")

        except Exception as e:
            self.log_message(f"加载配置失败: {e}")

    def reset_config(self):
        """重置配置为默认值"""
        try:
            if messagebox.askyesno('确认', '确定要重置所有配置为默认值吗？'):
                # 重置界面控件
                self.interval_entry.delete(0, tk.END)
                self.interval_entry.insert(0, '1.0')

                self.action_var.set('keyboard')
                self.on_action_type_change()

                self.key_entry.delete(0, tk.END)
                self.key_entry.insert(0, 'a')
                self.mouse_button_var.set('left')

                self.mouse_x_entry.delete(0, tk.END)
                self.mouse_x_entry.insert(0, '0')

                self.mouse_y_entry.delete(0, tk.END)
                self.mouse_y_entry.insert(0, '0')

                self.limit_var.set(False)
                self.on_limit_change()

                self.total_clicks = 0
                self.total_clicks_label.config(text='总点击次数: 0')

                # 重置快捷键设置
                self.start_hotkey_combo = "ctrl+f9"
                self.stop_hotkey_combo = "ctrl+f10"
                self.pause_hotkey_combo = "ctrl+f11"

                self.start_hotkey_entry.delete(0, tk.END)
                self.start_hotkey_entry.insert(0, self.start_hotkey_combo)

                self.stop_hotkey_entry.delete(0, tk.END)
                self.stop_hotkey_entry.insert(0, self.stop_hotkey_combo)

                self.pause_hotkey_entry.delete(0, tk.END)
                self.pause_hotkey_entry.insert(0, self.pause_hotkey_combo)

                # 更新按钮文本
                self.start_btn.config(text=f'开始 ({self.start_hotkey_combo})')
                self.stop_btn.config(text=f'停止 ({self.stop_hotkey_combo})')
                self.pause_btn.config(text=f'暂停 ({self.pause_hotkey_combo})')

                # 重新启动快捷键监听
                if self.keyboard_listener:
                    self.keyboard_listener.stop()
                self.start_global_hotkeys()

                # 删除配置文件
                if os.path.exists(self.config_file):
                    os.remove(self.config_file)

                self.log_message("配置已重置为默认值")
                messagebox.showinfo('成功', '配置已重置为默认值')

        except Exception as e:
            self.log_message(f"重置配置失败: {e}")
            messagebox.showerror('错误', f'重置配置失败: {e}')

    def on_closing(self):
        """窗口关闭时的处理"""
        try:
            # 停止点击
            if self.running:
                self.stop_clicking()

            # 保存配置
            self.save_config()

            # 停止全局快捷键监听
            if self.keyboard_listener:
                self.keyboard_listener.stop()

            # 销毁窗口
            self.root.destroy()

        except Exception as e:
            print(f"关闭程序时发生错误: {e}")
            self.root.destroy()


def main():
    """主函数"""
    try:
        # 创建主窗口
        root = tk.Tk()

        # 创建应用实例
        app = AutoClicker(root)

        # 启动主循环
        root.mainloop()

    except Exception as e:
        print(f"程序启动失败: {e}")
        messagebox.showerror('启动错误', f'程序启动失败: {e}')


if __name__ == '__main__':
    main()