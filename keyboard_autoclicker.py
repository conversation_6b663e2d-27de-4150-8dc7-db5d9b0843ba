import tkinter as tk
from tkinter import messagebox, ttk
import threading
import time
import pyautogui

class AutoClicker:
    def __init__(self, root):
        self.root = root
        self.root.title('键盘自动点击器')

        self.running = False
        self.interval = 1.0
        self.key = 'a'

        # 界面组件
        tk.Label(root, text='点击间隔（秒）:').pack(pady=5)
        self.interval_entry = tk.Entry(root)
        self.interval_entry.pack(pady=5)
        self.interval_entry.insert(0, '1')

        tk.Label(root, text='目标按键:').pack(pady=5)
        self.key_combobox = ttk.Combobox(root, values=pyautogui.KEYBOARD_KEYS, state='readonly')
        self.key_combobox.pack(pady=5)
        self.key_combobox.set('a')  # 设置默认值

        self.start_btn = tk.Button(root, text='开始', command=self.start_clicking)
        self.start_btn.pack(pady=10)

        self.stop_btn = tk.Button(root, text='停止', command=self.stop_clicking, state=tk.DISABLED)
        self.stop_btn.pack(pady=10)

        # 自定义快捷键设置
        tk.Label(root, text='启动快捷键（如F1）:').pack(pady=5)
        self.start_shortcut_entry = tk.Entry(root)
        self.start_shortcut_entry.pack(pady=5)
        self.start_shortcut_entry.insert(0, 'F1')

        tk.Label(root, text='暂停快捷键（如F2）:').pack(pady=5)
        self.stop_shortcut_entry = tk.Entry(root)
        self.stop_shortcut_entry.pack(pady=5)
        self.stop_shortcut_entry.insert(0, 'F2')

        # 初始化快捷键绑定
        self.update_shortcuts()

    def start_clicking(self):
        try:
            self.interval = float(self.interval_entry.get())
            if self.interval <= 0:
                raise ValueError
            self.key = self.key_combobox.get().strip()
            if not self.key:
                raise ValueError
        except ValueError:
            messagebox.showerror('错误', '请输入有效的间隔（大于0）和按键')
            return

        self.running = True
        self.start_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        threading.Thread(target=self.click_loop, daemon=True).start()

    def stop_clicking_by_shortcut(self, event=None):
        if self.running:
            self.stop_clicking()

    def stop_clicking(self):
        self.running = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)

    def start_clicking_by_shortcut(self, event=None):
        if not self.running:
            self.start_clicking()

    def update_shortcuts(self):
        start_shortcut = self.start_shortcut_entry.get().strip()
        stop_shortcut = self.stop_shortcut_entry.get().strip()
        if not start_shortcut or not stop_shortcut:
            messagebox.showerror('错误', '快捷键不能为空')
            return
        try:
            self.root.unbind(f'<{self.start_shortcut}>')
            self.root.unbind(f'<{self.stop_shortcut}>')
        except:
            pass
        self.start_shortcut = start_shortcut
        self.stop_shortcut = stop_shortcut
        self.root.bind(f'<{self.start_shortcut}>', self.start_clicking_by_shortcut)
        self.root.bind(f'<{self.stop_shortcut}>', self.stop_clicking_by_shortcut)
        messagebox.showinfo('提示', '快捷键设置已更新')

    def click_loop(self):
        while self.running:
            pyautogui.press(self.key)
            time.sleep(self.interval)

if __name__ == '__main__':
    root = tk.Tk()
    app = AutoClicker(root)
    root.mainloop()