import tkinter as tk
from tkinter import messagebox, ttk
import threading
import time
import json
import os
from datetime import datetime
import sys

try:
    import pyautogui
    import pynput
    from pynput import keyboard, mouse
    from pynput.keyboard import Key, Listener as KeyboardListener
    from pynput.mouse import <PERSON><PERSON>, Listener as MouseListener
except ImportError as e:
    print(f"缺少必要的库: {e}")
    print("请运行: pip install pyautogui pynput")
    sys.exit(1)

class AutoClicker:
    def __init__(self, root):
        self.root = root
        self.root.title('高级自动点击器 v2.0')
        self.root.geometry('450x600')
        self.root.resizable(False, False)

        # 设置窗口图标和样式
        self.setup_window_style()

        # 状态变量
        self.running = False
        self.paused = False
        self.click_count = 0
        self.total_clicks = 0
        self.start_time = None

        # 配置变量
        self.interval = 1.0
        self.action_type = "keyboard"  # keyboard 或 mouse
        self.key = 'a'
        self.mouse_button = 'left'
        self.click_limit = 0  # 0表示无限制
        self.mouse_x = 0
        self.mouse_y = 0

        # 全局快捷键监听器
        self.keyboard_listener = None
        self.start_hotkey = {Key.f1}
        self.stop_hotkey = {Key.f2}
        self.pause_hotkey = {Key.f3}
        self.current_keys = set()

        # 配置文件路径
        self.config_file = "autoclicker_config.json"

        # 创建界面
        self.create_widgets()

        # 加载配置
        self.load_config()

        # 启动全局快捷键监听
        self.start_global_hotkeys()

        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_window_style(self):
        """设置窗口样式"""
        try:
            # 设置窗口在任务栏中的图标
            self.root.iconbitmap(default='')
        except:
            pass

        # 设置主题色
        self.bg_color = '#f0f0f0'
        self.accent_color = '#0078d4'
        self.root.configure(bg=self.bg_color)

    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg=self.bg_color)
        title_frame.pack(fill='x', padx=10, pady=10)

        title_label = tk.Label(title_frame, text='高级自动点击器',
                              font=('微软雅黑', 16, 'bold'),
                              bg=self.bg_color, fg=self.accent_color)
        title_label.pack()

        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=5)

        # 基本设置标签页
        self.basic_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.basic_frame, text='基本设置')
        self.create_basic_settings()

        # 高级设置标签页
        self.advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.advanced_frame, text='高级设置')
        self.create_advanced_settings()

        # 状态监控标签页
        self.status_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.status_frame, text='状态监控')
        self.create_status_monitor()

        # 控制按钮
        self.create_control_buttons()

    def create_basic_settings(self):
        """创建基本设置界面"""
        # 动作类型选择
        action_frame = ttk.LabelFrame(self.basic_frame, text='动作类型', padding=10)
        action_frame.pack(fill='x', padx=10, pady=5)

        self.action_var = tk.StringVar(value="keyboard")
        ttk.Radiobutton(action_frame, text='键盘按键', variable=self.action_var,
                       value='keyboard', command=self.on_action_type_change).pack(anchor='w')
        ttk.Radiobutton(action_frame, text='鼠标点击', variable=self.action_var,
                       value='mouse', command=self.on_action_type_change).pack(anchor='w')

        # 键盘设置框架
        self.keyboard_frame = ttk.LabelFrame(self.basic_frame, text='键盘设置', padding=10)
        self.keyboard_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(self.keyboard_frame, text='目标按键:').pack(anchor='w')
        self.key_combobox = ttk.Combobox(self.keyboard_frame, width=20, state='readonly')
        self.key_combobox.pack(fill='x', pady=2)

        # 填充常用按键
        common_keys = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm',
                      'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
                      '0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                      'space', 'enter', 'tab', 'shift', 'ctrl', 'alt',
                      'f1', 'f2', 'f3', 'f4', 'f5', 'f6', 'f7', 'f8', 'f9', 'f10', 'f11', 'f12',
                      'up', 'down', 'left', 'right', 'home', 'end', 'pageup', 'pagedown']
        self.key_combobox['values'] = common_keys
        self.key_combobox.set('a')

        # 鼠标设置框架
        self.mouse_frame = ttk.LabelFrame(self.basic_frame, text='鼠标设置', padding=10)
        self.mouse_frame.pack(fill='x', padx=10, pady=5)

        # 鼠标按钮选择
        button_frame = ttk.Frame(self.mouse_frame)
        button_frame.pack(fill='x', pady=2)

        ttk.Label(button_frame, text='鼠标按钮:').pack(side='left')
        self.mouse_button_var = tk.StringVar(value="left")
        ttk.Radiobutton(button_frame, text='左键', variable=self.mouse_button_var,
                       value='left').pack(side='left', padx=10)
        ttk.Radiobutton(button_frame, text='右键', variable=self.mouse_button_var,
                       value='right').pack(side='left', padx=10)
        ttk.Radiobutton(button_frame, text='中键', variable=self.mouse_button_var,
                       value='middle').pack(side='left', padx=10)

        # 鼠标位置设置
        pos_frame = ttk.Frame(self.mouse_frame)
        pos_frame.pack(fill='x', pady=5)

        ttk.Label(pos_frame, text='点击位置:').pack(anchor='w')

        coord_frame = ttk.Frame(pos_frame)
        coord_frame.pack(fill='x', pady=2)

        ttk.Label(coord_frame, text='X:').pack(side='left')
        self.mouse_x_entry = ttk.Entry(coord_frame, width=8)
        self.mouse_x_entry.pack(side='left', padx=5)

        ttk.Label(coord_frame, text='Y:').pack(side='left', padx=(10,0))
        self.mouse_y_entry = ttk.Entry(coord_frame, width=8)
        self.mouse_y_entry.pack(side='left', padx=5)

        ttk.Button(coord_frame, text='获取当前位置',
                  command=self.get_current_mouse_pos).pack(side='left', padx=10)

        # 时间间隔设置
        interval_frame = ttk.LabelFrame(self.basic_frame, text='时间设置', padding=10)
        interval_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(interval_frame, text='点击间隔（秒）:').pack(anchor='w')
        self.interval_entry = ttk.Entry(interval_frame, width=20)
        self.interval_entry.pack(fill='x', pady=2)
        self.interval_entry.insert(0, '1.0')

        # 点击次数限制
        limit_frame = ttk.Frame(interval_frame)
        limit_frame.pack(fill='x', pady=5)

        self.limit_var = tk.BooleanVar()
        ttk.Checkbutton(limit_frame, text='限制点击次数',
                       variable=self.limit_var,
                       command=self.on_limit_change).pack(side='left')

        self.limit_entry = ttk.Entry(limit_frame, width=10, state='disabled')
        self.limit_entry.pack(side='left', padx=10)

        # 初始化界面状态
        self.on_action_type_change()

    def create_advanced_settings(self):
        """创建高级设置界面"""
        # 全局快捷键设置
        hotkey_frame = ttk.LabelFrame(self.advanced_frame, text='全局快捷键', padding=10)
        hotkey_frame.pack(fill='x', padx=10, pady=5)

        ttk.Label(hotkey_frame, text='开始/停止: F1').pack(anchor='w', pady=2)
        ttk.Label(hotkey_frame, text='暂停/继续: F3').pack(anchor='w', pady=2)
        ttk.Label(hotkey_frame, text='紧急停止: F2').pack(anchor='w', pady=2)

        # 配置管理
        config_frame = ttk.LabelFrame(self.advanced_frame, text='配置管理', padding=10)
        config_frame.pack(fill='x', padx=10, pady=5)

        config_btn_frame = ttk.Frame(config_frame)
        config_btn_frame.pack(fill='x')

        ttk.Button(config_btn_frame, text='保存配置',
                  command=self.save_config).pack(side='left', padx=5)
        ttk.Button(config_btn_frame, text='加载配置',
                  command=self.load_config).pack(side='left', padx=5)
        ttk.Button(config_btn_frame, text='重置配置',
                  command=self.reset_config).pack(side='left', padx=5)

    def create_status_monitor(self):
        """创建状态监控界面"""
        # 运行状态
        status_frame = ttk.LabelFrame(self.status_frame, text='运行状态', padding=10)
        status_frame.pack(fill='x', padx=10, pady=5)

        self.status_label = ttk.Label(status_frame, text='状态: 已停止',
                                     font=('微软雅黑', 10, 'bold'))
        self.status_label.pack(anchor='w', pady=2)

        # 统计信息
        stats_frame = ttk.LabelFrame(self.status_frame, text='统计信息', padding=10)
        stats_frame.pack(fill='x', padx=10, pady=5)

        self.click_count_label = ttk.Label(stats_frame, text='当前会话点击次数: 0')
        self.click_count_label.pack(anchor='w', pady=2)

        self.total_clicks_label = ttk.Label(stats_frame, text='总点击次数: 0')
        self.total_clicks_label.pack(anchor='w', pady=2)

        self.runtime_label = ttk.Label(stats_frame, text='运行时间: 00:00:00')
        self.runtime_label.pack(anchor='w', pady=2)

        # 日志区域
        log_frame = ttk.LabelFrame(self.status_frame, text='操作日志', padding=10)
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # 创建文本框和滚动条
        log_text_frame = ttk.Frame(log_frame)
        log_text_frame.pack(fill='both', expand=True)

        self.log_text = tk.Text(log_text_frame, height=8, wrap='word', state='disabled')
        log_scrollbar = ttk.Scrollbar(log_text_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side='left', fill='both', expand=True)
        log_scrollbar.pack(side='right', fill='y')

        # 清除日志按钮
        ttk.Button(log_frame, text='清除日志', command=self.clear_log).pack(pady=5)

    def create_control_buttons(self):
        """创建控制按钮"""
        control_frame = tk.Frame(self.root, bg=self.bg_color)
        control_frame.pack(fill='x', padx=10, pady=10)

        # 主要控制按钮
        btn_frame = tk.Frame(control_frame, bg=self.bg_color)
        btn_frame.pack()

        self.start_btn = tk.Button(btn_frame, text='开始 (F1)',
                                  command=self.start_clicking,
                                  bg='#28a745', fg='white',
                                  font=('微软雅黑', 10, 'bold'),
                                  width=12, height=2)
        self.start_btn.pack(side='left', padx=5)

        self.pause_btn = tk.Button(btn_frame, text='暂停 (F3)',
                                  command=self.pause_clicking,
                                  bg='#ffc107', fg='black',
                                  font=('微软雅黑', 10, 'bold'),
                                  width=12, height=2, state='disabled')
        self.pause_btn.pack(side='left', padx=5)

        self.stop_btn = tk.Button(btn_frame, text='停止 (F2)',
                                 command=self.stop_clicking,
                                 bg='#dc3545', fg='white',
                                 font=('微软雅黑', 10, 'bold'),
                                 width=12, height=2, state='disabled')
        self.stop_btn.pack(side='left', padx=5)

    def on_action_type_change(self):
        """动作类型改变时的处理"""
        if self.action_var.get() == 'keyboard':
            self.keyboard_frame.pack(fill='x', padx=10, pady=5)
            self.mouse_frame.pack_forget()
        else:
            self.mouse_frame.pack(fill='x', padx=10, pady=5)
            self.keyboard_frame.pack_forget()

    def on_limit_change(self):
        """点击次数限制改变时的处理"""
        if self.limit_var.get():
            self.limit_entry.config(state='normal')
            self.limit_entry.delete(0, tk.END)
            self.limit_entry.insert(0, '100')
        else:
            self.limit_entry.config(state='disabled')

    def get_current_mouse_pos(self):
        """获取当前鼠标位置"""
        try:
            x, y = pyautogui.position()
            self.mouse_x_entry.delete(0, tk.END)
            self.mouse_x_entry.insert(0, str(x))
            self.mouse_y_entry.delete(0, tk.END)
            self.mouse_y_entry.insert(0, str(y))
            self.log_message(f"获取鼠标位置: ({x}, {y})")
        except Exception as e:
            self.log_message(f"获取鼠标位置失败: {e}")

    def validate_settings(self):
        """验证设置参数"""
        try:
            # 验证间隔时间
            interval = float(self.interval_entry.get())
            if interval <= 0:
                raise ValueError("间隔时间必须大于0")
            self.interval = interval

            # 验证点击次数限制
            if self.limit_var.get():
                limit = int(self.limit_entry.get())
                if limit <= 0:
                    raise ValueError("点击次数限制必须大于0")
                self.click_limit = limit
            else:
                self.click_limit = 0

            # 验证动作类型相关设置
            if self.action_var.get() == 'keyboard':
                self.action_type = 'keyboard'
                self.key = self.key_combobox.get().strip()
                if not self.key:
                    raise ValueError("请选择目标按键")
            else:
                self.action_type = 'mouse'
                self.mouse_button = self.mouse_button_var.get()
                try:
                    self.mouse_x = int(self.mouse_x_entry.get())
                    self.mouse_y = int(self.mouse_y_entry.get())
                except ValueError:
                    raise ValueError("请输入有效的鼠标坐标")

            return True

        except ValueError as e:
            messagebox.showerror('设置错误', str(e))
            return False
        except Exception as e:
            messagebox.showerror('未知错误', f"验证设置时发生错误: {e}")
            return False

    def start_clicking(self):
        """开始点击"""
        if self.running:
            return

        if not self.validate_settings():
            return

        self.running = True
        self.paused = False
        self.click_count = 0
        self.start_time = datetime.now()

        # 更新界面状态
        self.start_btn.config(state='disabled')
        self.pause_btn.config(state='normal')
        self.stop_btn.config(state='normal')
        self.status_label.config(text='状态: 运行中')

        # 启动点击线程
        self.click_thread = threading.Thread(target=self.click_loop, daemon=True)
        self.click_thread.start()

        # 启动状态更新线程
        self.status_thread = threading.Thread(target=self.update_status, daemon=True)
        self.status_thread.start()

        action_desc = f"{'键盘按键: ' + self.key if self.action_type == 'keyboard' else '鼠标点击: ' + self.mouse_button + '键'}"
        self.log_message(f"开始自动点击 - {action_desc}, 间隔: {self.interval}秒")

    def pause_clicking(self):
        """暂停/继续点击"""
        if not self.running:
            return

        self.paused = not self.paused
        if self.paused:
            self.pause_btn.config(text='继续 (F3)')
            self.status_label.config(text='状态: 已暂停')
            self.log_message("暂停自动点击")
        else:
            self.pause_btn.config(text='暂停 (F3)')
            self.status_label.config(text='状态: 运行中')
            self.log_message("继续自动点击")

    def stop_clicking(self):
        """停止点击"""
        if not self.running:
            return

        self.running = False
        self.paused = False

        # 更新界面状态
        self.start_btn.config(state='normal')
        self.pause_btn.config(state='disabled', text='暂停 (F3)')
        self.stop_btn.config(state='disabled')
        self.status_label.config(text='状态: 已停止')

        # 更新总点击次数
        self.total_clicks += self.click_count

        self.log_message(f"停止自动点击 - 本次点击: {self.click_count} 次")

    def click_loop(self):
        """点击循环"""
        while self.running:
            if not self.paused:
                try:
                    # 检查点击次数限制
                    if self.click_limit > 0 and self.click_count >= self.click_limit:
                        self.root.after(0, self.stop_clicking)
                        break

                    # 执行点击动作
                    if self.action_type == 'keyboard':
                        pyautogui.press(self.key)
                    else:
                        if self.mouse_button == 'left':
                            pyautogui.click(self.mouse_x, self.mouse_y, button='left')
                        elif self.mouse_button == 'right':
                            pyautogui.click(self.mouse_x, self.mouse_y, button='right')
                        elif self.mouse_button == 'middle':
                            pyautogui.click(self.mouse_x, self.mouse_y, button='middle')

                    self.click_count += 1

                except Exception as e:
                    self.log_message(f"执行点击时发生错误: {e}")
                    self.root.after(0, self.stop_clicking)
                    break

            time.sleep(self.interval)

    def update_status(self):
        """更新状态显示"""
        while self.running:
            if self.start_time:
                runtime = datetime.now() - self.start_time
                runtime_str = str(runtime).split('.')[0]  # 去掉微秒

                self.root.after(0, lambda: self.click_count_label.config(
                    text=f'当前会话点击次数: {self.click_count}'))
                self.root.after(0, lambda: self.total_clicks_label.config(
                    text=f'总点击次数: {self.total_clicks + self.click_count}'))
                self.root.after(0, lambda: self.runtime_label.config(
                    text=f'运行时间: {runtime_str}'))

            time.sleep(1)

    def start_global_hotkeys(self):
        """启动全局快捷键监听"""
        try:
            def on_press(key):
                self.current_keys.add(key)

                # 检查快捷键组合
                if self.start_hotkey.issubset(self.current_keys):
                    if not self.running:
                        self.root.after(0, self.start_clicking)
                    else:
                        self.root.after(0, self.stop_clicking)
                elif self.stop_hotkey.issubset(self.current_keys):
                    if self.running:
                        self.root.after(0, self.stop_clicking)
                elif self.pause_hotkey.issubset(self.current_keys):
                    if self.running:
                        self.root.after(0, self.pause_clicking)

            def on_release(key):
                try:
                    self.current_keys.remove(key)
                except KeyError:
                    pass

            self.keyboard_listener = KeyboardListener(
                on_press=on_press,
                on_release=on_release
            )
            self.keyboard_listener.start()

        except Exception as e:
            self.log_message(f"启动全局快捷键失败: {e}")

    def log_message(self, message):
        """添加日志消息"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            self.log_text.config(state='normal')
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
            self.log_text.config(state='disabled')
        except Exception as e:
            print(f"日志记录失败: {e}")

    def clear_log(self):
        """清除日志"""
        self.log_text.config(state='normal')
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state='disabled')

    def save_config(self):
        """保存配置到文件"""
        try:
            config = {
                'interval': self.interval_entry.get(),
                'action_type': self.action_var.get(),
                'key': self.key_combobox.get(),
                'mouse_button': self.mouse_button_var.get(),
                'mouse_x': self.mouse_x_entry.get(),
                'mouse_y': self.mouse_y_entry.get(),
                'click_limit_enabled': self.limit_var.get(),
                'click_limit': self.limit_entry.get(),
                'total_clicks': self.total_clicks
            }

            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

            self.log_message("配置已保存")
            messagebox.showinfo('成功', '配置已保存到文件')

        except Exception as e:
            self.log_message(f"保存配置失败: {e}")
            messagebox.showerror('错误', f'保存配置失败: {e}')

    def load_config(self):
        """从文件加载配置"""
        try:
            if not os.path.exists(self.config_file):
                return

            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 应用配置
            self.interval_entry.delete(0, tk.END)
            self.interval_entry.insert(0, config.get('interval', '1.0'))

            self.action_var.set(config.get('action_type', 'keyboard'))
            self.on_action_type_change()

            self.key_combobox.set(config.get('key', 'a'))
            self.mouse_button_var.set(config.get('mouse_button', 'left'))

            self.mouse_x_entry.delete(0, tk.END)
            self.mouse_x_entry.insert(0, config.get('mouse_x', '0'))

            self.mouse_y_entry.delete(0, tk.END)
            self.mouse_y_entry.insert(0, config.get('mouse_y', '0'))

            self.limit_var.set(config.get('click_limit_enabled', False))
            self.on_limit_change()
            if self.limit_var.get():
                self.limit_entry.delete(0, tk.END)
                self.limit_entry.insert(0, config.get('click_limit', '100'))

            self.total_clicks = config.get('total_clicks', 0)
            self.total_clicks_label.config(text=f'总点击次数: {self.total_clicks}')

            self.log_message("配置已加载")

        except Exception as e:
            self.log_message(f"加载配置失败: {e}")

    def reset_config(self):
        """重置配置为默认值"""
        try:
            if messagebox.askyesno('确认', '确定要重置所有配置为默认值吗？'):
                # 重置界面控件
                self.interval_entry.delete(0, tk.END)
                self.interval_entry.insert(0, '1.0')

                self.action_var.set('keyboard')
                self.on_action_type_change()

                self.key_combobox.set('a')
                self.mouse_button_var.set('left')

                self.mouse_x_entry.delete(0, tk.END)
                self.mouse_x_entry.insert(0, '0')

                self.mouse_y_entry.delete(0, tk.END)
                self.mouse_y_entry.insert(0, '0')

                self.limit_var.set(False)
                self.on_limit_change()

                self.total_clicks = 0
                self.total_clicks_label.config(text='总点击次数: 0')

                # 删除配置文件
                if os.path.exists(self.config_file):
                    os.remove(self.config_file)

                self.log_message("配置已重置为默认值")
                messagebox.showinfo('成功', '配置已重置为默认值')

        except Exception as e:
            self.log_message(f"重置配置失败: {e}")
            messagebox.showerror('错误', f'重置配置失败: {e}')

    def on_closing(self):
        """窗口关闭时的处理"""
        try:
            # 停止点击
            if self.running:
                self.stop_clicking()

            # 保存配置
            self.save_config()

            # 停止全局快捷键监听
            if self.keyboard_listener:
                self.keyboard_listener.stop()

            # 销毁窗口
            self.root.destroy()

        except Exception as e:
            print(f"关闭程序时发生错误: {e}")
            self.root.destroy()


def main():
    """主函数"""
    try:
        # 创建主窗口
        root = tk.Tk()

        # 创建应用实例
        app = AutoClicker(root)

        # 启动主循环
        root.mainloop()

    except Exception as e:
        print(f"程序启动失败: {e}")
        messagebox.showerror('启动错误', f'程序启动失败: {e}')


if __name__ == '__main__':
    main()