@echo off
echo ========================================
echo 高级自动点击器打包脚本
echo ========================================

echo.
echo 1. 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境，请先安装Python
    pause
    exit /b 1
)

echo.
echo 2. 安装依赖包...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo 错误: 依赖包安装失败
    pause
    exit /b 1
)

echo.
echo 3. 清理旧的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "AutoClicker.exe" del "AutoClicker.exe"

echo.
echo 4. 开始打包...
pyinstaller autoclicker.spec
if %errorlevel% neq 0 (
    echo 错误: 打包失败
    pause
    exit /b 1
)

echo.
echo 5. 复制可执行文件...
if exist "dist\AutoClicker.exe" (
    copy "dist\AutoClicker.exe" "AutoClicker.exe"
    echo 成功: AutoClicker.exe 已生成
) else (
    echo 错误: 未找到生成的可执行文件
    pause
    exit /b 1
)

echo.
echo 6. 清理临时文件...
rmdir /s /q "build"
rmdir /s /q "dist"
del "AutoClicker.spec"

echo.
echo ========================================
echo 打包完成！
echo 可执行文件: AutoClicker.exe
echo ========================================
echo.
echo 使用说明:
echo 1. 双击 AutoClicker.exe 运行程序
echo 2. 全局快捷键:
echo    - F1: 开始/停止
echo    - F2: 紧急停止
echo    - F3: 暂停/继续
echo 3. 支持键盘按键和鼠标点击
echo 4. 配置会自动保存
echo ========================================

pause
